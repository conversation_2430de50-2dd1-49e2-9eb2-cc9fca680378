#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include "App.h"

#if defined(QT_DEBUG) || defined(QT_QML_DEBUG)
#include <QDirIterator>
#endif

int main(int argc, char* argv[])
{
    Q_INIT_RESOURCE(qml_resources);
    QGuiApplication app(argc, argv);
    std::unique_ptr<QQmlApplicationEngine> applicationEngine = std::make_unique<QQmlApplicationEngine>();

    // Instantiate App, parented to the QGuiApplication to ensure its lifetime
    App application(applicationEngine.get(), &app);

    // Set the App instance as a context property before loading QML.
    applicationEngine->rootContext()->setContextProperty("AppInstance", &application);

    // Add QML import path for resources
    applicationEngine->addImportPath("qrc:/");

#if defined(QT_DEBUG) || defined(QT_QML_DEBUG)
    // Debug resource content
    qDebug() << "All resource files:";
    QDirIterator it(":/", QDirIterator::Subdirectories);
    while (it.hasNext())
    {
        qDebug() << it.next();
    }
#endif

    qmlRegisterType<App>("com.yourcompany.qmldaw", 1, 0, "App");
    const QUrl url(QStringLiteral("qrc:/qml/MainView.qml"));
    QMetaObject::Connection connection = QObject::connect(
            applicationEngine.get(), &QQmlApplicationEngine::objectCreated, &app,
            [url, &application](const QObject* obj, const QUrl& objUrl)
            {
                if (!obj && url == objUrl)
                    QCoreApplication::exit(-1);
            },
            Qt::QueuedConnection);
    applicationEngine->load(url);

    const int result = app.exec();

    // Disconnect the signal to prevent further QML object creation callbacks
    QObject::disconnect(connection);

    // Explicitly destroy QML root objects to trigger Component.onDestruction
    // and ensure QML cleanup before C++ objects are destroyed.
    if (!applicationEngine->rootObjects().isEmpty())
    {
        applicationEngine->rootObjects().first()->deleteLater();
    }

    applicationEngine->clearComponentCache();
    applicationEngine.reset();

    Q_CLEANUP_RESOURCE(qml_resources);

    return result;
}
